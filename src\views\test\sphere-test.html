<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="initial-scale=1.0, user-scalable=no, width=device-width"
    />
    <title>球体测试页面</title>
    <style>
      html,
      body,
      #container {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }
      .instructions {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 1000;
        font-size: 14px;
        max-width: 300px;
      }
    </style>
  </head>
  <body>
    <div id="container"></div>
    <div class="instructions">
      <h4>球体测试说明：</h4>
      <p>• 左键点击地图：创建3D球体</p>
      <p>• 右键点击球体附近：删除球体</p>
      <p>• 球体会自动用3D线条连接</p>
      <p>• 基于官方GLCustomLayer实现</p>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/three@0.142/build/three.js"></script>
    <script src="//webapi.amap.com/maps?v=2.0&key=您申请的key值"></script>

    <script type="text/javascript">
      var map = new AMap.Map('container', {
        center: [116.54, 39.79],
        zooms: [2, 20],
        zoom: 14,
        viewMode: '3D',
        pitch: 50,
      });

      var camera;
      var renderer;
      var scene;
      var sphereList = [];
      var lines = [];
      var customCoords = map.customCoords;
      var sphereGeometry;
      var sphereMaterial;

      // 创建 GL 图层
      var gllayer = new AMap.GLCustomLayer({
        zIndex: 10,
        init: (gl) => {
          camera = new THREE.PerspectiveCamera(
            60,
            window.innerWidth / window.innerHeight,
            100,
            1 << 30
          );

          renderer = new THREE.WebGLRenderer({
            context: gl,
          });

          renderer.autoClear = false;
          scene = new THREE.Scene();

          // 环境光照和平行光
          var aLight = new THREE.AmbientLight(0xffffff, 0.3);
          var dLight = new THREE.DirectionalLight(0xffffff, 1);
          dLight.position.set(1000, -100, 900);
          scene.add(dLight);
          scene.add(aLight);

          // 创建球体几何体和材质
          sphereGeometry = new THREE.SphereGeometry(200, 16, 16);
          sphereMaterial = new THREE.MeshPhongMaterial({
            color: 0xff6b6b,
            depthTest: true,
            transparent: true,
            opacity: 0.8
          });

          console.log('Three.js GLCustomLayer初始化完成');
        },
        render: () => {
          if (!renderer || !scene || !camera) return;

          renderer.resetState();
          
          if (sphereList.length > 0) {
            const firstSphere = sphereList[0];
            customCoords.setCenter([firstSphere.lng, firstSphere.lat]);
          }

          var { near, far, fov, up, lookAt, position } = customCoords.getCameraParams();

          camera.near = near;
          camera.far = far;
          camera.fov = fov;
          camera.position.set(...position);
          camera.up.set(...up);
          camera.lookAt(...lookAt);
          camera.updateProjectionMatrix();

          renderer.render(scene, camera);
          renderer.resetState();
        }
      });
      
      map.add(gllayer);

      // 地图点击事件 - 创建球体
      map.on('click', function(e) {
        createSphere(e.lnglat.lng, e.lnglat.lat);
      });

      // 地图右键事件 - 删除球体
      map.on('rightclick', function(e) {
        deleteNearestSphere(e.lnglat.lng, e.lnglat.lat);
      });

      // 创建球体
      function createSphere(lng, lat) {
        if (!scene || !customCoords) return;

        console.log('创建3D球体:', lng, lat);

        const coordsData = customCoords.lngLatsToCoords([[lng, lat]]);
        if (!coordsData || coordsData.length === 0) {
          console.error('坐标转换失败');
          return;
        }

        const coord = coordsData[0];
        const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial.clone());
        sphereMesh.position.set(coord[0], coord[1], 500);
        
        sphereMesh.userData = {
          id: Date.now() + Math.random(),
          type: 'sphere',
          lng: lng,
          lat: lat
        };

        scene.add(sphereMesh);

        sphereList.push({
          id: sphereMesh.userData.id,
          mesh: sphereMesh,
          lng: lng,
          lat: lat
        });

        updateLines();
        map.render();

        console.log('3D球体创建完成，球体总数:', sphereList.length);
      }

      // 删除最近的球体
      function deleteNearestSphere(lng, lat) {
        if (sphereList.length === 0) return;

        let nearestSphere = null;
        let minDistance = Infinity;

        sphereList.forEach(sphere => {
          const distance = getDistance({lng: lng, lat: lat}, {lng: sphere.lng, lat: sphere.lat});
          if (distance < minDistance) {
            minDistance = distance;
            nearestSphere = sphere;
          }
        });

        if (nearestSphere && minDistance < 0.001) { // 约100米范围内
          deleteSphere(nearestSphere.id);
        }
      }

      // 删除球体
      function deleteSphere(sphereId) {
        const sphereIndex = sphereList.findIndex(sphere => sphere.id === sphereId);
        if (sphereIndex === -1) return;

        const sphereData = sphereList[sphereIndex];
        
        if (sphereData.mesh && scene) {
          scene.remove(sphereData.mesh);
          if (sphereData.mesh.geometry) sphereData.mesh.geometry.dispose();
          if (sphereData.mesh.material) sphereData.mesh.material.dispose();
        }

        sphereList.splice(sphereIndex, 1);
        updateLines();
        map.render();

        console.log('删除球体完成，剩余球体数:', sphereList.length);
      }

      // 更新连接线
      function updateLines() {
        // 清理现有线条
        lines.forEach(line => {
          if (line.geometry) line.geometry.dispose();
          if (line.material) line.material.dispose();
          scene.remove(line);
        });
        lines = [];

        if (sphereList.length < 2) return;

        // 创建线条连接所有球体
        for (let i = 0; i < sphereList.length - 1; i++) {
          const sphere1 = sphereList[i];
          const sphere2 = sphereList[i + 1];

          const pos1 = sphere1.mesh.position;
          const pos2 = sphere2.mesh.position;

          // 创建曲线
          const midPoint = new THREE.Vector3(
            (pos1.x + pos2.x) / 2,
            (pos1.y + pos2.y) / 2 + 200,
            (pos1.z + pos2.z) / 2
          );

          const curve = new THREE.CatmullRomCurve3([
            new THREE.Vector3(pos1.x, pos1.y, pos1.z),
            midPoint,
            new THREE.Vector3(pos2.x, pos2.y, pos2.z)
          ]);

          const curvePoints = curve.getPoints(20);
          const geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);

          const material = new THREE.LineBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.8
          });

          const line = new THREE.Line(geometry, material);
          scene.add(line);
          lines.push(line);
        }

        console.log(`创建了 ${lines.length} 条3D线条连接 ${sphereList.length} 个球体`);
      }

      // 计算距离
      function getDistance(pos1, pos2) {
        const dlng = pos1.lng - pos2.lng;
        const dlat = pos1.lat - pos2.lat;
        return Math.sqrt(dlng * dlng + dlat * dlat);
      }

      // 窗口大小调整
      function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }
      window.addEventListener('resize', onWindowResize);
    </script>
  </body>
</html>
